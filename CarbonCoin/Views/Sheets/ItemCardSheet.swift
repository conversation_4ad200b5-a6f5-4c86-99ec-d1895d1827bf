//
//  ItemCardSheetView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/31.
//

import SwiftUI
import Foundation

// MARK: - 卡片Sheet视图
struct ItemCardSheetView: View {
    // MARK: - Properties
    @EnvironmentObject private var cardStore: CardStore
    @State private var sheetHeight: CGFloat = UIScreen.main.bounds.height / 4 // 初始高度
    @State private var isDragging = false
    @State private var draggedCard: ItemCard?

    // 屏幕尺寸
    private let screenHeight = UIScreen.main.bounds.height
    private let fullHeight: CGFloat
    private let compactHeight: CGFloat

    // 拖拽处理回调
    let onCardDropped: (String, String) -> Void // (cardId, userId)
    let onGlobalDragStarted: ((ItemCard, CGPoint) -> Void)?
    let onGlobalDragChanged: ((CGPoint) -> Void)?
    let onGlobalDragEnded: (() -> Void)?

    init(
        onCardDropped: @escaping (String, String) -> Void,
        onGlobalDragStarted: ((ItemCard, CGPoint) -> Void)? = nil,
        onGlobalDragChanged: ((CGPoint) -> Void)? = nil,
        onGlobalDragEnded: (() -> Void)? = nil
    ) {
        self.onCardDropped = onCardDropped
        self.onGlobalDragStarted = onGlobalDragStarted
        self.onGlobalDragChanged = onGlobalDragChanged
        self.onGlobalDragEnded = onGlobalDragEnded
        self.fullHeight = UIScreen.main.bounds.height / 4
        self.compactHeight = UIScreen.main.bounds.height / 6
    }

    var body: some View {
        VStack(spacing: 0) {
            // 拖拽指示器
            dragIndicator

            // 标题栏
            headerView

            // 卡片滚动视图
            cardScrollView

            Spacer()
        }
        .frame(height: sheetHeight)
        .frame(width: UIScreen.main.bounds.width * 0.95)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .fill(Color.cardBackground.opacity(0.95))
                .background(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                        .stroke(Color.cardBorderGradient, lineWidth: 1)
                )
        )
        .animation(.easeInOut(duration: 0.3), value: sheetHeight)
        .onAppear {
            // 加载用户卡片数据
            Task {
                await cardStore.loadUserCards()
            }
        }
    }

    // MARK: - 拖拽指示器
    private var dragIndicator: some View {
        RoundedRectangle(cornerRadius: 2)
            .fill(Color.textSecondary.opacity(0.5))
            .frame(width: 40, height: 4)
            .padding(.top, Theme.Spacing.sm)
    }

    // MARK: - 标题栏
    private var headerView: some View {
        HStack {
            Text("我的卡片")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            Spacer()

            Text("\(cardStore.cards.count) 张")
                .font(.captionBrand)
                .foregroundColor(.textSecondary)
        }
        .padding(.horizontal, Theme.Spacing.md)
        .padding(.vertical, Theme.Spacing.sm)
    }

    // MARK: - 卡片滚动视图
    private var cardScrollView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyHStack(spacing: Theme.Spacing.md) {
                ForEach(cardStore.cards) { card in
                    DraggableCardThumbnail(
                        card: card,
                        onDragStarted: handleDragStarted,
                        onDragChanged: handleDragChanged,
                        onDragEnded: handleDragEnded
                    )
                }
            }
            .padding(.horizontal, Theme.Spacing.md)
        }
        .frame(height: 160)
    }

    // MARK: - 拖拽处理方法

    /// 处理拖拽开始
    private func handleDragStarted(_ card: ItemCard) {
        draggedCard = card
        isDragging = true
        print("🎯 开始拖拽卡片: \(card.title)")

        // 触发全局拖拽开始
        if let onGlobalDragStarted = onGlobalDragStarted {
            // 计算卡片在屏幕中的初始位置
            let screenCenter = CGPoint(x: UIScreen.main.bounds.width / 2, y: UIScreen.main.bounds.height * 0.8)
            onGlobalDragStarted(card, screenCenter)
        }
    }

    /// 处理拖拽状态变化
    private func handleDragChanged(_ card: ItemCard, _ value: DragGesture.Value) {
        draggedCard = card
        isDragging = true

        // 更新全局拖拽位置
        if let onGlobalDragChanged = onGlobalDragChanged {
            let globalPosition = CGPoint(
                x: value.location.x,
                y: value.location.y
            )
            onGlobalDragChanged(globalPosition)
        }

        // TODO: 检查是否拖拽出sheet边界
//        let dragY = value.translation.height
//        print("dragY: \(dragY)")
//        
//        if dragY < -50 { // 向上拖拽超过50点
//            // 缩小sheet高度
//            withAnimation(.easeInOut(duration: 0.3)) {
//                sheetHeight = compactHeight
//            }
//        } else if dragY > -20 && sheetHeight == compactHeight {
//            // 拖拽回sheet区域，恢复高度
//            withAnimation(.easeInOut(duration: 0.3)) {
//                sheetHeight = fullHeight
//            }
//        }
    }

    /// 处理拖拽结束
    private func handleDragEnded(_ card: ItemCard, _ value: DragGesture.Value) {
        isDragging = false
        draggedCard = nil

        // 触发全局拖拽结束
        onGlobalDragEnded?()

        // 恢复sheet高度
//        withAnimation(.easeInOut(duration: 0.3)) {
//            sheetHeight = fullHeight
//        }
    }
}

// MARK: - 可拖拽卡片缩略图
struct DraggableCardThumbnail: View {
    let card: ItemCard
    let onDragStarted: (ItemCard) -> Void
    let onDragChanged: (ItemCard, DragGesture.Value) -> Void
    let onDragEnded: (ItemCard, DragGesture.Value) -> Void

    @State private var dragOffset = CGSize.zero
    @State private var isPressed = false
    @State private var isDragging = false
    @State private var navigateToDetail = false
    @GestureState private var gestureState: DragState = .inactive
    @EnvironmentObject var cardStore: CardStore

    // 拖拽状态枚举
    enum DragState {
        case inactive
        case longPressing
        case dragging(translation: CGSize)

        var translation: CGSize {
            switch self {
            case .dragging(let translation):
                return translation
            default:
                return .zero
            }
        }

        var isActive: Bool {
            switch self {
            case .inactive:
                return false
            default:
                return true
            }
        }
    }

    var body: some View {
        cardContent
            .onTapGesture {
                // 只有在非拖拽状态下才允许导航
                if !gestureState.isActive && !isDragging {
                    navigateToDetail = true
                }
            }
            .navigationDestination(isPresented: $navigateToDetail) {
                ItemCardDetailView(
                    userItemCard: UserItemCard(
                        id: UUID().uuidString,
                        userId: "",
                        cardId: card.id,
                        remark: nil,
                        acquiredAt: Date(),
                        isOwner: false,
                        card: card,
                        author: nil
                    ),
                    cardViewModel: ItemCardViewModel(itemCard: card),
                    cardStore: cardStore
                )
            }
    }

    // MARK: - 卡片内容视图
    private var cardContent: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
            // 卡片图片
            cardImage

            // 卡片标题
            cardTitle

            // 卡片标签
            cardTags
        }
        .frame(width: 120)
        .padding(Theme.Spacing.sm)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                .fill(Color.cardBackground.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                        .stroke(Color.brandGreen.opacity(isPressed ? 0.8 : 0.3), lineWidth: 1)
                )
        )
        .scaleEffect(gestureState.isActive ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: gestureState.isActive)
        .onDrag {
            // 系统级拖拽支持 - 提供带前缀的卡片ID作为拖拽数据
            return NSItemProvider(object: DragManager.createDraggableCardId(card.id) as NSString)
        }
        .simultaneousGesture(
            // 使用 simultaneousGesture 避免与点击手势冲突
            LongPressGesture(minimumDuration: 0.5)
                .onEnded { _ in
                    // 长按开始拖拽模式
                    isDragging = true
                    onDragStarted(card)
                }
        )
        .simultaneousGesture(
            DragGesture(coordinateSpace: .global)
                .onChanged { value in
                    if isDragging {
                        onDragChanged(card, value)
                    }
                }
                .onEnded { value in
                    if isDragging {
                        onDragEnded(card, value)
                        isDragging = false
                    }
                }
        )
    }

    // MARK: - 卡片图片
    private var cardImage: some View {
        Group {
            if let image = card.image {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFill()
                    .frame(height: 80)
                    .clipped()
                    .cornerRadius(Theme.CornerRadius.sm)
            } else {
                Rectangle()
                    .fill(Color.cardBackground.opacity(0.3))
                    .frame(height: 80)
                    .cornerRadius(Theme.CornerRadius.sm)
                    .overlay(
                        Image(systemName: "photo")
                            .font(.title3)
                            .foregroundColor(.textSecondary)
                    )
            }
        }
    }

    // MARK: - 卡片标题
    private var cardTitle: some View {
        Text(card.title)
            .font(.caption)
            .fontWeight(.medium)
            .foregroundColor(.textPrimary)
            .lineLimit(2)
            .multilineTextAlignment(.leading)
    }

    // MARK: - 卡片标签
    private var cardTags: some View {
        HStack {
            ForEach(Array(card.tags.prefix(1)), id: \.self) { tag in
                Text(tag)
                    .font(.caption2)
                    .foregroundColor(.textPrimary)
                    .padding(.horizontal, 4)
                    .padding(.vertical, 2)
                    .background(Color.brandGreen.opacity(0.2))
                    .cornerRadius(Theme.CornerRadius.sm)
            }

            if card.tags.count > 1 {
                Text("+\(card.tags.count - 1)")
                    .font(.caption2)
                    .foregroundColor(.textSecondary)
            }

            Spacer()
        }
    }
}

// MARK: - 预览
#Preview("卡片Sheet视图") {
    let sampleCards = [
        ItemCard(
            id: "card1",
            tags: ["塑料瓶", "可回收"],
            description: "这是一个塑料瓶",
            title: "可乐瓶",
            imageFileName: "",
            imageURL: "",
            createdAt: Date(),
            authorId: "user1",
            location: "北京市",
            latitude: 39.9042,
            longitude: 116.4074,
            cardType: .shopping,
            themeColor: "#FF5733"
        ),
        ItemCard(
            id: "card2",
            tags: ["纸质", "包装"],
            description: "这是一个纸盒",
            title: "快递盒",
            imageFileName: "",
            imageURL: "",
            createdAt: Date(),
            authorId: "user1",
            location: "上海市",
            latitude: 31.2304,
            longitude: 121.4737,
            cardType: .daily,
            themeColor: "#33C3F0"
        )
    ]

    Group {
        let cardStore = CardStore()
        // 在预览中设置示例数据
        let _ = cardStore.cards = sampleCards

        ItemCardSheetView { cardId, userId in
            print("卡片 \(cardId) 拖拽到用户 \(userId)")
        }
        .environmentObject(cardStore)
        .background(Color.black)
    }
}
